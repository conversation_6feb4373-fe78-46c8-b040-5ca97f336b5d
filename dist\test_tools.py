#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具測試腳本
驗證 mp4-merge 和 vidmerger 是否正常工作
"""

import os
import subprocess
import glob

def test_mp4_merge():
    """測試 MP4-Merge 工具"""
    print("🔧 測試 MP4-Merge 工具...")
    
    # 尋找 mp4-merge 執行檔
    pattern = "tools/mp4-merge/mp4_merge*.exe"
    matches = glob.glob(pattern)
    
    if not matches:
        print("❌ 未找到 MP4-Merge 執行檔")
        return False
    
    # 優先選擇 windows64 版本
    exe_path = None
    for match in matches:
        if 'windows64' in match:
            exe_path = match
            break
    if not exe_path:
        exe_path = matches[0]
    
    print(f"📁 找到執行檔: {exe_path}")
    
    # 測試工具是否可以運行
    try:
        result = subprocess.run([exe_path], capture_output=True, text=True, timeout=5)
        if "No input files!" in result.stdout or result.returncode == 0:
            print("✅ MP4-Merge 工具可以正常運行")
            return True
        else:
            print(f"❌ MP4-Merge 工具運行異常: {result.stdout}")
            return False
    except subprocess.TimeoutExpired:
        print("✅ MP4-Merge 工具可以正常運行 (超時但正常)")
        return True
    except Exception as e:
        print(f"❌ MP4-Merge 工具測試失敗: {e}")
        return False

def test_vidmerger():
    """測試 VidMerger 包裝器"""
    print("\n📼 測試 VidMerger 包裝器...")
    
    # 檢查包裝器檔案
    py_wrapper = "tools/vidmerger/vidmerger_wrapper.py"
    bat_wrapper = "tools/vidmerger/vidmerger.bat"
    
    if not os.path.exists(py_wrapper):
        print("❌ 未找到 VidMerger Python 包裝器")
        return False
    
    if not os.path.exists(bat_wrapper):
        print("❌ 未找到 VidMerger 批次檔包裝器")
        return False
    
    print("✅ VidMerger 包裝器檔案存在")
    
    # 測試 Python 包裝器
    try:
        result = subprocess.run(["python", py_wrapper, "--help"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 or "usage:" in result.stdout.lower():
            print("✅ VidMerger Python 包裝器可以正常運行")
            return True
        else:
            print(f"⚠️ VidMerger Python 包裝器可能有問題: {result.stderr}")
            return True  # 仍然可用，只是幫助信息可能不同
    except Exception as e:
        print(f"❌ VidMerger 包裝器測試失敗: {e}")
        return False

def test_ffmpeg():
    """測試 FFmpeg"""
    print("\n🎬 測試 FFmpeg...")
    
    try:
        result = subprocess.run(["ffmpeg", "-version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg 可用: {version_line}")
            return True
        else:
            print("❌ FFmpeg 不可用")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg 未安裝")
        return False
    except Exception as e:
        print(f"❌ FFmpeg 測試失敗: {e}")
        return False

def main():
    """主函數"""
    print("🧪 影片合併工具測試")
    print("=" * 40)
    
    # 測試各個工具
    mp4_ok = test_mp4_merge()
    vid_ok = test_vidmerger()
    ffmpeg_ok = test_ffmpeg()
    
    # 總結
    print("\n" + "=" * 40)
    print("📋 測試結果總結:")
    print(f"   MP4-Merge: {'✅ 正常' if mp4_ok else '❌ 異常'}")
    print(f"   VidMerger: {'✅ 正常' if vid_ok else '❌ 異常'}")
    print(f"   FFmpeg:    {'✅ 正常' if ffmpeg_ok else '❌ 異常'}")
    
    if mp4_ok and vid_ok:
        print("\n🎉 所有工具都可以正常使用！")
        print("💡 現在可以執行 'python video_merger_gui.py' 或 'main.exe.bat' 來啟動程式")
    else:
        print("\n⚠️ 部分工具可能有問題，請檢查安裝")
        if not ffmpeg_ok:
            print("💡 VidMerger 需要 FFmpeg，請安裝 FFmpeg")

if __name__ == "__main__":
    main()
