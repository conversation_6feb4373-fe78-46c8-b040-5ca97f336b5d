@echo off
echo 🎬 影片合併工具 EXE 打包程式
echo ================================

echo 📦 安裝 PyInstaller...
pip install pyinstaller

echo 🚀 開始打包...
pyinstaller --onefile --windowed --name "影片合併工具" ^
    --add-data "tools;tools" ^
    --add-data "mp4_merge_wrapper.py;." ^
    --add-data "tools/vidmerger/vidmerger_wrapper.py;tools/vidmerger" ^
    --add-data "tools/vidmerger/vidmerger.bat;tools/vidmerger" ^
    --add-data "README.md;." ^
    --add-data "使用說明.txt;." ^
    video_merger_gui.py

echo 📁 複製額外檔案...
copy "install_tools.py" "dist\"
copy "test_tools.py" "dist\"
copy "中文檔名問題解決方案.md" "dist\"
copy "requirements.txt" "dist\"

echo ✅ 打包完成！
echo 📁 輸出目錄: dist\
echo 🚀 執行檔案: dist\影片合併工具.exe

pause
