# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['video_merger_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('tools', 'tools'),
        ('mp4_merge_wrapper.py', '.'),
        ('tools/vidmerger/vidmerger_wrapper.py', 'tools/vidmerger'),
        ('tools/vidmerger/vidmerger.bat', 'tools/vidmerger'),
        ('README.md', '.'),
        ('使用說明.txt', '.'),
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyd = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyd,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='影片合併工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
