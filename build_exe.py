#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包腳本 - 將影片合併工具打包成 EXE
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """檢查 PyInstaller 是否已安裝"""
    try:
        import PyInstaller
        print("✅ PyInstaller 已安裝")
        return True
    except ImportError:
        print("❌ PyInstaller 未安裝")
        return False

def install_pyinstaller():
    """安裝 PyInstaller"""
    print("📦 安裝 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✅ PyInstaller 安裝完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 安裝失敗: {e}")
        return False

def create_spec_file():
    """創建 PyInstaller 規格檔案"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['video_merger_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('tools', 'tools'),
        ('mp4_merge_wrapper.py', '.'),
        ('tools/vidmerger/vidmerger_wrapper.py', 'tools/vidmerger'),
        ('tools/vidmerger/vidmerger.bat', 'tools/vidmerger'),
        ('README.md', '.'),
        ('使用說明.txt', '.'),
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyd = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyd,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='影片合併工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('video_merger.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 規格檔案已創建: video_merger.spec")

def build_exe():
    """執行打包"""
    print("🚀 開始打包...")
    
    try:
        # 使用規格檔案打包
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'video_merger.spec']
        
        print(f"📝 執行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(cmd, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT,
                                 text=True,
                                 encoding='utf-8',
                                 errors='replace')
        
        # 即時顯示輸出
        for line in process.stdout:
            print(line.strip())
        
        process.wait()
        
        if process.returncode == 0:
            print("✅ 打包完成！")
            return True
        else:
            print(f"❌ 打包失敗 (返回碼: {process.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 打包過程中發生錯誤: {e}")
        return False

def copy_additional_files():
    """複製額外檔案到輸出目錄"""
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ 找不到 dist 目錄")
        return False
    
    print("📁 複製額外檔案...")
    
    # 要複製的檔案
    files_to_copy = [
        "install_tools.py",
        "test_tools.py",
        "中文檔名問題解決方案.md",
        "requirements.txt"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            try:
                shutil.copy2(file_name, dist_dir)
                print(f"   ✅ {file_name}")
            except Exception as e:
                print(f"   ❌ {file_name}: {e}")
        else:
            print(f"   ⚠️ {file_name} 不存在")
    
    return True

def create_readme():
    """創建 EXE 版本的說明檔案"""
    readme_content = """# 🎬 影片合併工具 EXE 版本

## 🚀 快速開始
1. 雙擊 "影片合併工具.exe" 啟動程式
2. 選擇適合的工具：
   - 🔧 MP4-Merge: 無損合併相同格式的 MP4 檔案
   - 📼 VidMerger: 合併多種格式的影片檔案

## 📁 檔案說明
- 影片合併工具.exe - 主程式
- tools/ - 工具目錄（包含 mp4-merge 和 vidmerger）
- 使用說明.txt - 詳細使用說明
- 中文檔名問題解決方案.md - 技術說明

## 🔧 系統需求
- Windows 10/11 (64位元)
- 建議安裝 FFmpeg（VidMerger 功能需要）

## 💡 使用提示
- 支援中文檔名
- 自動生成輸出檔名（第一個檔案名_merge.mp4）
- 可以自由修改輸出檔名

## 🎯 特色功能
✅ 無損 MP4 合併
✅ 多格式影片合併
✅ 中文檔名支援
✅ 圖形化介面
✅ 進度顯示
✅ 錯誤處理

享受影片合併的樂趣！🎉
"""
    
    with open("dist/EXE版本說明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("✅ EXE版本說明已創建")

def main():
    """主函數"""
    print("🎬 影片合併工具 EXE 打包程式")
    print("=" * 50)
    
    # 檢查 PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("❌ 無法安裝 PyInstaller，打包失敗")
            return
    
    # 創建規格檔案
    create_spec_file()
    
    # 執行打包
    if build_exe():
        # 複製額外檔案
        copy_additional_files()
        
        # 創建說明檔案
        create_readme()
        
        print("\n" + "=" * 50)
        print("🎉 打包完成！")
        print("📁 輸出目錄: dist/")
        print("🚀 執行檔案: dist/影片合併工具.exe")
        print("\n💡 您可以將整個 dist/ 目錄複製到其他電腦使用")
    else:
        print("\n❌ 打包失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    main()
