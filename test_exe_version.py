#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 EXE 版本功能
"""

import os
import subprocess
import time

def test_exe_exists():
    """測試 EXE 檔案是否存在"""
    exe_path = "dist/影片合併工具.exe"
    if os.path.exists(exe_path):
        size = os.path.getsize(exe_path)
        print(f"✅ EXE 檔案存在: {exe_path}")
        print(f"📊 檔案大小: {size:,} bytes ({size/1024/1024:.1f} MB)")
        return True
    else:
        print(f"❌ EXE 檔案不存在: {exe_path}")
        return False

def test_exe_launch():
    """測試 EXE 檔案是否能啟動"""
    exe_path = "dist/影片合併工具.exe"
    print("🚀 測試 EXE 檔案啟動...")
    
    try:
        # 啟動程式但不等待（因為是 GUI 程式）
        process = subprocess.Popen([exe_path])
        
        # 等待一下讓程式啟動
        time.sleep(3)
        
        # 檢查程式是否還在運行
        if process.poll() is None:
            print("✅ EXE 程式成功啟動並運行中")
            
            # 終止程式
            process.terminate()
            time.sleep(1)
            
            if process.poll() is not None:
                print("✅ EXE 程式正常終止")
            else:
                process.kill()
                print("⚠️ EXE 程式需要強制終止")
            
            return True
        else:
            print(f"❌ EXE 程式啟動後立即退出 (返回碼: {process.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ EXE 程式啟動失敗: {e}")
        return False

def check_dist_files():
    """檢查 dist 目錄中的檔案"""
    print("📁 檢查 dist 目錄檔案...")
    
    expected_files = [
        "影片合併工具.exe",
        "EXE版本說明.txt",
        "使用說明_EXE版本.txt",
        "install_tools.py",
        "test_tools.py",
        "中文檔名問題解決方案.md",
        "requirements.txt"
    ]
    
    dist_dir = "dist"
    if not os.path.exists(dist_dir):
        print(f"❌ dist 目錄不存在")
        return False
    
    missing_files = []
    existing_files = []
    
    for file_name in expected_files:
        file_path = os.path.join(dist_dir, file_name)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            existing_files.append((file_name, size))
            print(f"   ✅ {file_name} ({size:,} bytes)")
        else:
            missing_files.append(file_name)
            print(f"   ❌ {file_name}")
    
    if missing_files:
        print(f"\n⚠️ 缺少檔案: {', '.join(missing_files)}")
        return False
    else:
        print(f"\n✅ 所有檔案都存在 ({len(existing_files)} 個)")
        return True

def calculate_total_size():
    """計算 dist 目錄總大小"""
    dist_dir = "dist"
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk(dist_dir):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.exists(file_path):
                total_size += os.path.getsize(file_path)
                file_count += 1
    
    print(f"📊 dist 目錄統計:")
    print(f"   檔案數量: {file_count}")
    print(f"   總大小: {total_size:,} bytes ({total_size/1024/1024:.1f} MB)")
    
    return total_size

def main():
    """主函數"""
    print("🧪 EXE 版本功能測試")
    print("=" * 40)
    
    # 測試項目
    tests = [
        ("檢查 EXE 檔案", test_exe_exists),
        ("檢查 dist 檔案", check_dist_files),
        ("計算總大小", calculate_total_size),
        ("測試 EXE 啟動", test_exe_launch),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試失敗: {e}")
            results.append((test_name, False))
    
    # 總結
    print("\n" + "=" * 40)
    print("📋 測試結果總結:")
    
    passed = 0
    for test_name, result in results:
        if isinstance(result, bool):
            status = "✅ 通過" if result else "❌ 失敗"
            if result:
                passed += 1
        else:
            status = "✅ 完成"
            passed += 1
        
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 測試通過率: {passed}/{len(results)} ({passed/len(results)*100:.0f}%)")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！EXE 版本準備就緒！")
        print("💡 您可以將 dist/ 目錄複製到其他電腦使用")
    else:
        print("\n⚠️ 部分測試失敗，請檢查問題")

if __name__ == "__main__":
    main()
