#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VidMerger 包裝腳本
使用 FFmpeg 實現類似 vidmerger 的功能
"""

import os
import sys
import subprocess
import glob
from pathlib import Path

class VidMergerWrapper:
    def __init__(self):
        self.supported_formats = [
            '3g2', '3gp', 'aac', 'ac3', 'alac', 'amr', 'ape', 'au', 'avi', 'awb',
            'dts', 'f4a', 'f4b', 'f4p', 'f4v', 'flac', 'flv', 'm4a', 'm4b', 'm4p',
            'm4r', 'm4v', 'mkv', 'mov', 'mp2', 'mp3', 'mp4', 'mpeg', 'mpg', 'oga',
            'ogg', 'ogm', 'ogv', 'ogx', 'opus', 'pcm', 'spx', 'wav', 'webm', 'wma', 'wmv'
        ]
    
    def find_video_files(self, directory, format_filter=None):
        """在目錄中尋找影片檔案"""
        video_files = []
        formats = format_filter if format_filter else self.supported_formats
        
        for fmt in formats:
            pattern = os.path.join(directory, f"*.{fmt}")
            files = glob.glob(pattern, recursive=False)
            video_files.extend(files)
        
        # 排序檔案
        video_files.sort()
        return video_files
    
    def get_video_info(self, file_path):
        """獲取影片資訊"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                import json
                return json.loads(result.stdout)
        except Exception:
            pass
        return None
    
    def create_concat_file(self, video_files, concat_file_path):
        """創建 FFmpeg concat 檔案"""
        with open(concat_file_path, 'w', encoding='utf-8') as f:
            for video_file in video_files:
                # 使用絕對路徑並轉義特殊字符
                abs_path = os.path.abspath(video_file)
                escaped_path = abs_path.replace('\\', '\\\\').replace("'", "\\'")
                f.write(f"file '{escaped_path}'\n")
    
    def extract_chapter_title(self, filename):
        """從檔名提取章節標題"""
        # 移除副檔名
        name = Path(filename).stem
        
        # 尋找第一個 '-' 到副檔名之間的文字作為章節標題
        if ' - ' in name:
            parts = name.split(' - ', 1)
            if len(parts) > 1:
                return parts[1].strip()
        
        # 如果沒有找到，使用檔名
        return name
    
    def merge_videos(self, directory, output_name=None, formats=None, verbose=False):
        """合併影片"""
        print(f"🔍 掃描目錄: {directory}")
        
        # 尋找影片檔案
        video_files = self.find_video_files(directory, formats)
        
        if not video_files:
            print("❌ 未找到支援的影片檔案")
            return False
        
        if len(video_files) < 2:
            print("❌ 需要至少兩個影片檔案才能合併")
            return False
        
        print(f"📁 找到 {len(video_files)} 個影片檔案:")
        for i, file in enumerate(video_files, 1):
            print(f"   {i}. {os.path.basename(file)}")
        
        # 確認合併順序
        print("\n📋 合併順序:")
        for i, file in enumerate(video_files, 1):
            chapter_title = self.extract_chapter_title(file)
            print(f"   {i}. {chapter_title}")
        
        # 設定輸出檔名
        if not output_name:
            # 使用第一個檔案的副檔名
            first_ext = Path(video_files[0]).suffix
            output_name = f"output{first_ext}"
        
        output_path = os.path.join(directory, output_name)
        
        # 創建臨時 concat 檔案
        concat_file = os.path.join(directory, "temp_concat.txt")
        
        try:
            self.create_concat_file(video_files, concat_file)
            
            # 執行 FFmpeg 合併
            print(f"\n🚀 開始合併影片...")
            print(f"📤 輸出檔案: {output_name}")
            
            cmd = [
                'ffmpeg', '-f', 'concat', '-safe', '0',
                '-i', concat_file,
                '-c', 'copy',  # 複製流，不重新編碼
                '-y',  # 覆蓋輸出檔案
                output_path
            ]
            
            if verbose:
                print(f"📝 執行命令: {' '.join(cmd)}")
            
            # 執行命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE if not verbose else None,
                stderr=subprocess.STDOUT if not verbose else None,
                text=True
            )
            
            if verbose:
                # 即時顯示輸出
                for line in process.stdout:
                    print(line.strip())
            
            process.wait()
            
            if process.returncode == 0:
                print("✅ 影片合併完成！")
                print(f"📁 輸出檔案: {output_path}")
                return True
            else:
                print(f"❌ 合併失敗 (返回碼: {process.returncode})")
                return False
                
        except Exception as e:
            print(f"❌ 執行錯誤: {e}")
            return False
        finally:
            # 清理臨時檔案
            if os.path.exists(concat_file):
                os.remove(concat_file)

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description="VidMerger 包裝腳本 - 使用 FFmpeg 合併影片")
    parser.add_argument("directory", help="包含影片檔案的目錄")
    parser.add_argument("-f", "--format", help="指定要合併的格式 (例如: mp4,avi)")
    parser.add_argument("-o", "--output", help="輸出檔案名稱")
    parser.add_argument("-v", "--verbose", action="store_true", help="顯示詳細日誌")
    parser.add_argument("-y", "--yes", action="store_true", help="跳過確認")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.directory):
        print(f"❌ 目錄不存在: {args.directory}")
        sys.exit(1)
    
    # 解析格式
    formats = None
    if args.format:
        formats = [fmt.strip() for fmt in args.format.split(',')]
    
    # 創建合併器
    merger = VidMergerWrapper()
    
    # 執行合併
    success = merger.merge_videos(
        args.directory,
        args.output,
        formats,
        args.verbose
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
