#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影片合併工具 GUI
整合 mp4-merge 和 vidmerger 兩個工具的圖形化介面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
from pathlib import Path

class VideoMergerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎬 影片合併工具")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 設置工具路徑
        self.mp4_merge_path = self.find_tool_path("mp4-merge", "mp4_merge.exe")
        self.vidmerger_path = self.find_tool_path("vidmerger", "vidmerger.exe")
        
        self.setup_ui()
        self.check_tools()
    
    def find_tool_path(self, tool_dir, exe_name):
        """尋找工具執行檔路徑"""
        possible_paths = [
            f"tools/{tool_dir}/{exe_name}",
            f"tools/{tool_dir}/bin/{exe_name}",
            f"{tool_dir}/{exe_name}",
            exe_name
        ]

        # 特殊處理 mp4-merge
        if tool_dir == "mp4-merge":
            # 尋找任何 mp4_merge 開頭的 exe 檔案，優先選擇 x64 版本
            import glob
            pattern = f"tools/{tool_dir}/mp4_merge*.exe"
            matches = glob.glob(pattern)
            if matches:
                # 優先選擇 windows64 版本
                for match in matches:
                    if 'windows64' in match:
                        return os.path.abspath(match)
                # 如果沒有 windows64，選擇第一個
                return os.path.abspath(matches[0])

        # 特殊處理 vidmerger
        if tool_dir == "vidmerger":
            # 優先使用批次檔包裝器
            bat_path = f"tools/{tool_dir}/vidmerger.bat"
            if os.path.exists(bat_path):
                return os.path.abspath(bat_path)
            # 或者 Python 包裝器
            py_path = f"tools/{tool_dir}/vidmerger_wrapper.py"
            if os.path.exists(py_path):
                return f"python {os.path.abspath(py_path)}"

        for path in possible_paths:
            if os.path.exists(path):
                return os.path.abspath(path)
        return None
    
    def setup_ui(self):
        """設置使用者介面"""
        # 標題
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=20)
        
        title_label = tk.Label(title_frame, 
                              text="🎬 影片合併工具", 
                              font=("Arial", 24, "bold"),
                              bg='#f0f0f0',
                              fg='#333333')
        title_label.pack()
        
        subtitle_label = tk.Label(title_frame,
                                 text="選擇適合的工具來合併您的影片",
                                 font=("Arial", 12),
                                 bg='#f0f0f0',
                                 fg='#666666')
        subtitle_label.pack(pady=(5, 0))
        
        # 工具選擇區域
        tools_frame = tk.Frame(self.root, bg='#f0f0f0')
        tools_frame.pack(pady=30, padx=50, fill='x')
        
        # mp4-merge 按鈕
        mp4_frame = tk.Frame(tools_frame, bg='white', relief='raised', bd=2)
        mp4_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        mp4_title = tk.Label(mp4_frame,
                            text="🔧 MP4-Merge",
                            font=("Arial", 16, "bold"),
                            bg='white',
                            fg='#2c3e50')
        mp4_title.pack(pady=(15, 5))
        
        mp4_desc = tk.Label(mp4_frame,
                           text="無損合併 MP4 檔案\n適用於相同設備錄製的分段影片\n保留所有原始軌道和元數據",
                           font=("Arial", 10),
                           bg='white',
                           fg='#7f8c8d',
                           justify='center')
        mp4_desc.pack(pady=(0, 15))
        
        self.mp4_button = tk.Button(mp4_frame,
                                   text="選擇 MP4 檔案合併",
                                   font=("Arial", 12, "bold"),
                                   bg='#3498db',
                                   fg='white',
                                   activebackground='#2980b9',
                                   activeforeground='white',
                                   command=self.use_mp4_merge,
                                   cursor='hand2',
                                   relief='flat',
                                   bd=0,
                                   pady=10)
        self.mp4_button.pack(pady=(0, 15), padx=15, fill='x')
        
        # vidmerger 按鈕
        vid_frame = tk.Frame(tools_frame, bg='white', relief='raised', bd=2)
        vid_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        vid_title = tk.Label(vid_frame,
                            text="📼 VidMerger",
                            font=("Arial", 16, "bold"),
                            bg='white',
                            fg='#2c3e50')
        vid_title.pack(pady=(15, 5))
        
        vid_desc = tk.Label(vid_frame,
                           text="基於 FFmpeg 的影片合併\n支援多種格式和 FPS 調整\n自動生成章節標題",
                           font=("Arial", 10),
                           bg='white',
                           fg='#7f8c8d',
                           justify='center')
        vid_desc.pack(pady=(0, 15))
        
        self.vid_button = tk.Button(vid_frame,
                                   text="選擇資料夾批量合併",
                                   font=("Arial", 12, "bold"),
                                   bg='#e74c3c',
                                   fg='white',
                                   activebackground='#c0392b',
                                   activeforeground='white',
                                   command=self.use_vidmerger,
                                   cursor='hand2',
                                   relief='flat',
                                   bd=0,
                                   pady=10)
        self.vid_button.pack(pady=(0, 15), padx=15, fill='x')
        
        # 狀態顯示區域
        status_frame = tk.Frame(self.root, bg='#f0f0f0')
        status_frame.pack(pady=20, padx=50, fill='both', expand=True)
        
        status_label = tk.Label(status_frame,
                               text="📋 執行狀態",
                               font=("Arial", 14, "bold"),
                               bg='#f0f0f0',
                               fg='#333333')
        status_label.pack(anchor='w')
        
        self.status_text = scrolledtext.ScrolledText(status_frame,
                                                    height=10,
                                                    font=("Consolas", 10),
                                                    bg='#2c3e50',
                                                    fg='#ecf0f1',
                                                    insertbackground='#ecf0f1')
        self.status_text.pack(fill='both', expand=True, pady=(10, 0))
        
        # 底部按鈕
        bottom_frame = tk.Frame(self.root, bg='#f0f0f0')
        bottom_frame.pack(pady=10, padx=50, fill='x')
        
        clear_button = tk.Button(bottom_frame,
                                text="清除日誌",
                                font=("Arial", 10),
                                bg='#95a5a6',
                                fg='white',
                                activebackground='#7f8c8d',
                                command=self.clear_log,
                                cursor='hand2',
                                relief='flat',
                                bd=0)
        clear_button.pack(side='left')
        
        exit_button = tk.Button(bottom_frame,
                               text="退出程式",
                               font=("Arial", 10),
                               bg='#e67e22',
                               fg='white',
                               activebackground='#d35400',
                               command=self.root.quit,
                               cursor='hand2',
                               relief='flat',
                               bd=0)
        exit_button.pack(side='right')
    
    def check_tools(self):
        """檢查工具是否可用"""
        self.log("🔍 檢查工具可用性...")
        
        if self.mp4_merge_path and os.path.exists(self.mp4_merge_path):
            self.log(f"✅ MP4-Merge 找到: {self.mp4_merge_path}")
        else:
            self.log("❌ MP4-Merge 未找到")
            self.mp4_button.config(state='disabled', bg='#bdc3c7')
        
        if self.vidmerger_path and os.path.exists(self.vidmerger_path):
            self.log(f"✅ VidMerger 找到: {self.vidmerger_path}")
        else:
            self.log("❌ VidMerger 未找到")
            self.vid_button.config(state='disabled', bg='#bdc3c7')
        
        # 檢查 FFmpeg
        try:
            subprocess.run(['ffmpeg', '-version'], 
                          capture_output=True, check=True)
            self.log("✅ FFmpeg 可用")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("⚠️ FFmpeg 未安裝 (VidMerger 需要)")
    
    def log(self, message):
        """添加日誌訊息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def clear_log(self):
        """清除日誌"""
        self.status_text.delete(1.0, tk.END)
    
    def use_mp4_merge(self):
        """使用 MP4-Merge 工具"""
        if not self.mp4_merge_path:
            messagebox.showerror("錯誤", "MP4-Merge 工具未找到！\n請執行 install_tools.py 安裝工具。")
            return
        
        files = filedialog.askopenfilenames(
            title="選擇要合併的 MP4 檔案",
            filetypes=[("MP4 files", "*.mp4"), ("All files", "*.*")]
        )
        
        if not files:
            return
        
        if len(files) < 2:
            messagebox.showwarning("警告", "請至少選擇兩個檔案進行合併！")
            return
        
        output_file = filedialog.asksaveasfilename(
            title="選擇輸出檔案",
            defaultextension=".mp4",
            filetypes=[("MP4 files", "*.mp4")]
        )
        
        if not output_file:
            return
        
        self.run_mp4_merge(files, output_file)
    


    def run_mp4_merge(self, input_files, output_file):
        """執行 MP4-Merge"""
        def run():
            try:
                self.log(f"🚀 開始使用 MP4-Merge 合併 {len(input_files)} 個檔案...")

                # 使用 Python 包裝器來避免編碼問題
                wrapper_cmd = ["python", "mp4_merge_wrapper.py"] + list(input_files) + ["--out", output_file]
                self.log(f"📝 使用包裝器執行合併...")

                # 設置環境變數
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'

                process = subprocess.Popen(wrapper_cmd,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.STDOUT,
                                         text=True,
                                         encoding='utf-8',
                                         errors='replace',
                                         env=env)

                for line in process.stdout:
                    if line.strip():
                        self.log(line.strip())

                process.wait()

                if process.returncode == 0:
                    self.log("✅ MP4-Merge 合併完成！")
                    messagebox.showinfo("成功", f"影片合併完成！\n輸出檔案: {output_file}")
                else:
                    self.log(f"❌ MP4-Merge 執行失敗 (返回碼: {process.returncode})")
                    messagebox.showerror("錯誤", "影片合併失敗！請檢查日誌。")

            except Exception as e:
                self.log(f"❌ 執行錯誤: {str(e)}")
                messagebox.showerror("錯誤", f"執行失敗: {str(e)}")

        threading.Thread(target=run, daemon=True).start()
    
    def use_vidmerger(self):
        """使用 VidMerger 工具"""
        if not self.vidmerger_path:
            messagebox.showerror("錯誤", "VidMerger 工具未找到！\n請執行 install_tools.py 安裝工具。")
            return
        
        directory = filedialog.askdirectory(title="選擇包含影片檔案的資料夾")
        
        if not directory:
            return
        
        self.run_vidmerger(directory)
    
    def run_vidmerger(self, directory):
        """執行 VidMerger"""
        def run():
            try:
                self.log(f"🚀 開始使用 VidMerger 合併資料夾: {directory}")

                # 處理不同類型的 vidmerger 路徑
                if self.vidmerger_path.startswith("python "):
                    # Python 包裝器
                    cmd_parts = self.vidmerger_path.split(" ", 1)
                    cmd = [cmd_parts[0], cmd_parts[1], directory, "-y", "--verbose"]
                elif self.vidmerger_path.endswith(".bat"):
                    # 批次檔包裝器
                    cmd = [self.vidmerger_path, directory, "-y", "--verbose"]
                else:
                    # 原始 vidmerger
                    cmd = [self.vidmerger_path, directory, "-y"]

                self.log(f"📝 執行命令: {' '.join(cmd)}")

                # 設置環境變數來處理中文編碼
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'

                process = subprocess.Popen(cmd,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.STDOUT,
                                         text=True,
                                         encoding='utf-8',
                                         errors='replace',
                                         env=env,
                                         cwd=directory,
                                         shell=True if self.vidmerger_path.endswith(".bat") else False)

                for line in process.stdout:
                    if line.strip():
                        self.log(line.strip())

                process.wait()

                if process.returncode == 0:
                    self.log("✅ VidMerger 合併完成！")
                    messagebox.showinfo("成功", f"影片合併完成！\n請檢查資料夾: {directory}")
                else:
                    self.log(f"❌ VidMerger 執行失敗 (返回碼: {process.returncode})")
                    messagebox.showerror("錯誤", "影片合併失敗！請檢查日誌。")

            except Exception as e:
                self.log(f"❌ 執行錯誤: {str(e)}")
                messagebox.showerror("錯誤", f"執行失敗: {str(e)}")

        threading.Thread(target=run, daemon=True).start()

def main():
    """主函數"""
    root = tk.Tk()
    app = VideoMergerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
