🎬 影片合併工具 EXE 版本 - 使用說明
==========================================

📋 版本資訊
----------
- 版本：EXE 獨立執行版本
- 檔案大小：約 11.4MB
- 支援系統：Windows 10/11 (64位元)
- 包含工具：MP4-Merge + VidMerger

🚀 快速開始
----------
1. 雙擊 "影片合併工具.exe" 啟動程式
2. 程式會顯示兩個選項按鍵：
   - 🔧 MP4-Merge: 選擇多個 MP4 檔案進行無損合併
   - 📼 VidMerger: 選擇資料夾進行批量合併

🎯 新功能特色
------------
✅ 自動生成輸出檔名
   - 格式：第一個檔案名_merge.mp4
   - 例如：Per best2-信義割頸男狠殺女公關_merge.mp4
   - 用戶可以自由修改檔名

✅ 完整中文支援
   - 支援任何中文檔名
   - 自動處理編碼問題
   - 無需手動轉換

✅ 獨立執行
   - 無需安裝 Python
   - 包含所有必要工具
   - 一鍵啟動

📁 檔案結構
----------
影片合併工具.exe          # 主程式（雙擊啟動）
EXE版本說明.txt           # 本檔案
install_tools.py          # 工具安裝腳本（備用）
test_tools.py             # 工具測試腳本
中文檔名問題解決方案.md    # 技術說明
requirements.txt          # Python 套件需求

🔧 工具比較
----------
MP4-Merge：
✅ 無損合併（不重新編碼）
✅ 保留所有軌道和元數據
✅ 速度極快
✅ 適合相同格式的 MP4 檔案

VidMerger：
✅ 支援多種影片格式
✅ 自動 FPS 調整
✅ 生成章節標題
✅ 批量處理整個資料夾

💡 使用技巧
----------
1. 選擇 MP4-Merge 當：
   - 檔案都是 MP4 格式
   - 來自相同設備/軟體
   - 想要最快速度和最佳品質

2. 選擇 VidMerger 當：
   - 檔案格式不同（mp4, avi, mkv 等）
   - 需要批量處理
   - FPS 不一致需要調整

🎯 操作流程
----------
MP4-Merge 操作：
1. 點擊 "🔧 MP4-Merge" 按鍵
2. 選擇要合併的 MP4 檔案（可多選）
3. 系統自動填入輸出檔名：第一個檔案名_merge.mp4
4. 可以修改檔名，然後點擊儲存
5. 等待合併完成

VidMerger 操作：
1. 點擊 "📼 VidMerger" 按鍵
2. 選擇包含影片檔案的資料夾
3. 系統自動掃描並合併該資料夾中的所有影片
4. 輸出檔案會在同一資料夾中

📊 效能表現
----------
- MP4 無損合併：通常 < 1 秒
- 多格式合併：依檔案大小而定
- 記憶體使用：約 50-100MB
- 支援大檔案：無限制

🔍 故障排除
----------
如果遇到問題：
1. 檢查程式內的日誌輸出
2. 確認檔案沒有被其他程式佔用
3. 檢查磁碟空間是否足夠
4. 重新啟動程式

常見問題：
Q: VidMerger 無法使用？
A: 需要安裝 FFmpeg，請從 https://ffmpeg.org 下載

Q: 合併後檔案很大？
A: VidMerger 會重新編碼，建議用 MP4-Merge 進行無損合併

Q: 程式無法啟動？
A: 確認系統是 Windows 10/11 64位元

🎉 享受使用
----------
這個 EXE 版本包含了所有功能，可以：
- 複製到任何 Windows 電腦使用
- 無需安裝額外軟體（除了 FFmpeg）
- 支援所有中文檔名
- 自動生成合理的輸出檔名

祝您使用愉快！🎬✨

技術支援：參考 "中文檔名問題解決方案.md" 獲取更多技術細節
