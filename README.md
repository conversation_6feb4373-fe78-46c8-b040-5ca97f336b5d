# 🎬 影片合併工具整合程式

這是一個整合了兩個熱門 GitHub 影片合併工具的 Python GUI 應用程式：

## 🔧 整合的工具

### 1. MP4-Merge (⭐ 86 stars)
- **特色**: 無損合併多個 .mp4 檔案
- **適用情境**: 相同裝置、相同設定下分割錄影的影片檔
- **優點**: 合併時不重新編碼，保留所有原始音訊與影片軌道資訊
- **GitHub**: [gyroflow/mp4-merge](https://github.com/gyroflow/mp4-merge)

### 2. VidMerger (⭐ 143 stars)
- **特色**: 基於 ffmpeg 的 CLI 工具
- **適用情境**: 批次影片合併，單一資料夾下快速操作
- **優點**: 可處理多種格式、自動跳 FPS 不一致問題、生成章節標題
- **GitHub**: [tgotwig/vidmerger](https://github.com/tgotwig/vidmerger)

## 📋 快速比較

| 項目 | MP4-Merge | VidMerger |
|------|-----------|-----------|
| **合併方式** | 無損合併（不重新編碼） | 基於 FFmpeg 重新編碼 |
| **支援格式** | 主要 MP4 | 多種格式 (mp4, avi, mkv 等) |
| **適合情境** | 拍攝分段影片後合併 | 批次影片合併 |
| **特殊功能** | 保留所有軌與 metadata | FPS 調整、章節標題生成 |

## 🚀 安裝與使用

### 步驟 1: 安裝工具
```bash
python install_tools.py
```

這個腳本會自動：
- 下載 mp4-merge 和 vidmerger 的 Windows 執行檔
- 安裝必要的 Python 套件
- 檢查 FFmpeg 是否已安裝

### 步驟 2: 啟動程式
```bash
python video_merger_gui.py
```

### 步驟 3: 選擇工具
程式會顯示兩個按鍵：
- **🔧 MP4-Merge**: 選擇多個 MP4 檔案進行無損合併
- **📼 VidMerger**: 選擇資料夾進行批量合併

## 📁 專案結構

```
merge Video/
├── install_tools.py          # 工具安裝腳本
├── video_merger_gui.py       # 主程式 GUI
├── README.md                 # 說明文件
└── tools/                    # 工具目錄
    ├── mp4-merge/           # MP4-Merge 工具
    └── vidmerger/           # VidMerger 工具
```

## ⚙️ 系統需求

- **作業系統**: Windows (主要支援)
- **Python**: 3.6 或更高版本
- **FFmpeg**: VidMerger 需要 (可選，但建議安裝)

### FFmpeg 安裝
VidMerger 需要 FFmpeg 才能運作。請從 [FFmpeg 官網](https://ffmpeg.org/download.html) 下載並安裝。

## 🎯 使用場景

### 使用 MP4-Merge 當：
- ✅ 你有相同攝影機錄製的分段 MP4 檔案
- ✅ 想要無損合併（不重新編碼）
- ✅ 需要保留所有原始軌道和元數據
- ✅ 檔案格式和設定完全相同

### 使用 VidMerger 當：
- ✅ 你有不同格式的影片檔案
- ✅ 需要批量處理整個資料夾
- ✅ 影片的 FPS 不一致需要調整
- ✅ 想要自動生成章節標題
- ✅ 可以接受重新編碼

## 🔧 進階功能

### MP4-Merge 特色
- 拖放檔案支援
- 保留所有音訊和影片軌道
- 保留元數據
- 支援大於 4GB 的檔案

### VidMerger 特色
- 自動 FPS 調整
- 章節標題生成
- 支援多種影片格式
- 批量處理

## 🐛 故障排除

### 常見問題

1. **工具未找到**
   - 確保已執行 `install_tools.py`
   - 檢查 `tools/` 目錄是否存在

2. **VidMerger 無法運作**
   - 確保已安裝 FFmpeg
   - 檢查 FFmpeg 是否在系統 PATH 中

3. **MP4-Merge 合併失敗**
   - 確保所有檔案都是相同格式和設定
   - 檢查檔案是否損壞

## 📞 支援

如果遇到問題：
1. 檢查程式內的日誌輸出
2. 確認工具的原始 GitHub 頁面的說明
3. 檢查系統需求是否滿足

## 📄 授權

本專案整合了開源工具，請參考各工具的原始授權：
- MP4-Merge: Apache-2.0, MIT
- VidMerger: 請參考原專案

---

🎉 **享受影片合併的樂趣！**
