#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試中文檔名處理
"""

import os
import subprocess
import tempfile
import shutil

def test_mp4_merge_with_chinese():
    """測試 MP4-Merge 處理中文檔名"""
    print("🧪 測試 MP4-Merge 中文檔名處理...")
    
    # 找到工具
    import glob
    pattern = "tools/mp4-merge/mp4_merge*.exe"
    matches = glob.glob(pattern)
    
    if not matches:
        print("❌ 未找到 MP4-Merge 工具")
        return
    
    exe_path = None
    for match in matches:
        if 'windows64' in match:
            exe_path = match
            break
    if not exe_path:
        exe_path = matches[0]
    
    print(f"📁 使用工具: {exe_path}")
    
    # 自動尋找測試檔案
    import glob
    test_pattern = "test video/*.mp4"
    all_files = glob.glob(test_pattern)

    # 過濾掉已經合併的檔案
    existing_files = [f for f in all_files if not any(keyword in f for keyword in ['合併', 'merge', 'output'])]
    
    if len(existing_files) < 2:
        print("❌ 需要至少兩個測試檔案")
        return
    
    print(f"📁 找到 {len(existing_files)} 個測試檔案")
    
    # 創建臨時檔案來避免中文檔名問題
    temp_files = []
    try:
        print("📝 創建臨時檔案...")
        for i, original_file in enumerate(existing_files):
            temp_fd, temp_path = tempfile.mkstemp(suffix=".mp4", prefix=f"test_{i+1}_")
            os.close(temp_fd)
            shutil.copy2(original_file, temp_path)
            temp_files.append(temp_path)
            print(f"   {os.path.basename(original_file)} → {os.path.basename(temp_path)}")
        
        # 創建輸出檔案
        output_temp = tempfile.mktemp(suffix=".mp4", prefix="merged_")
        
        # 執行合併
        cmd = [exe_path] + temp_files + ["--out", output_temp]
        print(f"🚀 執行合併...")
        print(f"📝 命令: {os.path.basename(exe_path)} [臨時檔案] --out [輸出檔案]")
        
        process = subprocess.Popen(cmd,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.STDOUT,
                                 text=True,
                                 encoding='utf-8',
                                 errors='replace')
        
        output_lines = []
        for line in process.stdout:
            if line.strip():
                output_lines.append(line.strip())
                print(f"   {line.strip()}")
        
        process.wait()
        
        if process.returncode == 0:
            print("✅ 合併成功！")
            
            # 移動到最終位置
            final_output = "test video/中文檔名測試_合併.mp4"
            shutil.move(output_temp, final_output)
            print(f"📁 輸出檔案: {final_output}")
            
            # 檢查檔案大小
            if os.path.exists(final_output):
                size = os.path.getsize(final_output)
                print(f"📊 檔案大小: {size:,} bytes")
            
        else:
            print(f"❌ 合併失敗 (返回碼: {process.returncode})")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
    finally:
        # 清理臨時檔案
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass

def main():
    """主函數"""
    print("🧪 中文檔名處理測試")
    print("=" * 40)
    
    test_mp4_merge_with_chinese()

if __name__ == "__main__":
    main()
