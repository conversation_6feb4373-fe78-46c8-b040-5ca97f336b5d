🎬 影片合併工具整合程式 - 使用說明
==========================================

📋 專案概述
----------
本程式整合了兩個 GitHub 上最受歡迎的影片合併工具：
1. MP4-Merge (⭐ 86 stars) - 無損合併 MP4 檔案
2. VidMerger (⭐ 143 stars) - 基於 FFmpeg 的多格式影片合併

🚀 快速開始
----------
1. 雙擊 "main.exe.bat" 啟動程式
2. 選擇適合的工具：
   - 🔧 MP4-Merge: 無損合併相同格式的 MP4 檔案
   - 📼 VidMerger: 合併多種格式的影片檔案

📁 專案結構
----------
merge Video/
├── main.exe.bat              # 主程式啟動器 (您要的 main.exe)
├── video_merger_gui.py       # GUI 主程式
├── install_tools.py          # 工具安裝腳本
├── README.md                 # 詳細說明文件
├── 使用說明.txt              # 本檔案
├── test video/               # 您的測試影片
└── tools/                    # 工具目錄
    ├── mp4-merge/           # MP4-Merge 工具
    │   └── mp4_merge-windows-arm64.exe
    └── vidmerger/           # VidMerger 包裝器
        ├── vidmerger.bat
        └── vidmerger_wrapper.py

🔧 工具特色比較
--------------
MP4-Merge:
✅ 無損合併（不重新編碼）
✅ 保留所有軌道和元數據
✅ 適合相同設備錄製的分段影片
✅ 速度快，品質無損失

VidMerger:
✅ 支援多種影片格式
✅ 自動 FPS 調整
✅ 生成章節標題
✅ 批量處理整個資料夾

💡 使用建議
----------
選擇 MP4-Merge 當：
- 您有相同攝影機錄製的分段 MP4 檔案
- 想要最快速度和最佳品質
- 檔案格式和設定完全相同

選擇 VidMerger 當：
- 您有不同格式的影片檔案
- 需要批量處理整個資料夾
- 影片的 FPS 不一致需要調整

🎯 實際操作
----------
1. 啟動程式：雙擊 "main.exe.bat"
2. 程式會顯示兩個按鍵選項
3. 選擇適合的工具
4. 按照提示選擇檔案或資料夾
5. 等待合併完成

📝 注意事項
----------
- MP4-Merge 已成功安裝並可使用
- VidMerger 使用自製的 Python 包裝器（功能相同）
- 確保已安裝 FFmpeg（VidMerger 需要）
- 建議先用測試影片試用功能

🔍 故障排除
----------
如果遇到問題：
1. 檢查程式內的日誌輸出
2. 確認 FFmpeg 已正確安裝
3. 檢查影片檔案是否損壞
4. 參考 README.md 獲取更多資訊

🎉 享受影片合併的樂趣！
======================
