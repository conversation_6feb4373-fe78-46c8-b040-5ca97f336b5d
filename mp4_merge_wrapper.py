#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MP4-Merge 包裝器
解決中文檔名編碼問題
"""

import os
import sys
import subprocess
import tempfile
import shutil
import glob

def find_mp4_merge():
    """尋找 MP4-Merge 執行檔"""
    pattern = "tools/mp4-merge/mp4_merge*.exe"
    matches = glob.glob(pattern)
    
    if not matches:
        return None
    
    # 優先選擇 windows64 版本
    for match in matches:
        if 'windows64' in match:
            return os.path.abspath(match)
    
    return os.path.abspath(matches[0])

def create_safe_filename(original_path):
    """創建安全的檔案名稱"""
    _, ext = os.path.splitext(original_path)
    temp_fd, temp_path = tempfile.mkstemp(suffix=ext, prefix="video_")
    os.close(temp_fd)
    shutil.copy2(original_path, temp_path)
    return temp_path

def merge_videos(input_files, output_file):
    """合併影片檔案"""
    mp4_merge_path = find_mp4_merge()
    if not mp4_merge_path:
        print("❌ 未找到 MP4-Merge 工具")
        return False
    
    temp_files = []
    try:
        print(f"🚀 開始合併 {len(input_files)} 個檔案...")
        
        # 檢查是否需要使用臨時檔案
        need_temp_files = any(not all(ord(c) < 128 for c in f) for f in input_files)
        need_temp_output = not all(ord(c) < 128 for c in output_file)
        
        if need_temp_files:
            print("📝 檔案名稱包含非ASCII字符，使用臨時檔案...")
            safe_input_files = []
            for input_file in input_files:
                if not all(ord(c) < 128 for c in input_file):
                    temp_file = create_safe_filename(input_file)
                    temp_files.append(temp_file)
                    safe_input_files.append(temp_file)
                    print(f"   {os.path.basename(input_file)} → {os.path.basename(temp_file)}")
                else:
                    safe_input_files.append(input_file)
        else:
            safe_input_files = list(input_files)
        
        # 處理輸出檔案
        if need_temp_output:
            temp_output = tempfile.mktemp(suffix=".mp4", prefix="output_")
            temp_files.append(temp_output)
        else:
            temp_output = output_file
        
        # 執行 MP4-Merge
        cmd = [mp4_merge_path] + safe_input_files + ["--out", temp_output]
        print(f"📝 執行: {os.path.basename(mp4_merge_path)} [檔案列表] --out [輸出檔案]")
        
        process = subprocess.Popen(cmd,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.STDOUT,
                                 text=True,
                                 encoding='utf-8',
                                 errors='replace')
        
        # 顯示進度
        for line in process.stdout:
            if line.strip():
                print(f"   {line.strip()}")
        
        process.wait()
        
        if process.returncode == 0:
            # 移動到最終位置
            if need_temp_output and temp_output != output_file:
                shutil.move(temp_output, output_file)
                print(f"📁 檔案已移動到: {output_file}")
            
            print("✅ 合併完成！")
            return True
        else:
            print(f"❌ 合併失敗 (返回碼: {process.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ 執行錯誤: {e}")
        return False
    finally:
        # 清理臨時檔案
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass

def main():
    """主函數"""
    if len(sys.argv) < 4:
        print("用法: python mp4_merge_wrapper.py <輸入檔案1> <輸入檔案2> ... --out <輸出檔案>")
        sys.exit(1)
    
    args = sys.argv[1:]
    
    # 尋找 --out 參數
    if "--out" not in args:
        print("❌ 缺少 --out 參數")
        sys.exit(1)
    
    out_index = args.index("--out")
    input_files = args[:out_index]
    output_file = args[out_index + 1]
    
    if len(input_files) < 2:
        print("❌ 需要至少兩個輸入檔案")
        sys.exit(1)
    
    # 檢查檔案是否存在
    for input_file in input_files:
        if not os.path.exists(input_file):
            print(f"❌ 檔案不存在: {input_file}")
            sys.exit(1)
    
    # 執行合併
    success = merge_videos(input_files, output_file)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
