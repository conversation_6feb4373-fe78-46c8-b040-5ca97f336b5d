#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影片合併工具安裝腳本
自動下載 mp4-merge 和 vidmerger 工具
"""

import os
import sys
import requests
import zipfile
import platform
from pathlib import Path
import subprocess

def check_python_version():
    """檢查 Python 版本"""
    if sys.version_info < (3, 6):
        print("❌ 需要 Python 3.6 或更高版本")
        sys.exit(1)
    print(f"✅ Python 版本: {sys.version}")

def install_requirements():
    """安裝必要的 Python 套件"""
    requirements = [
        'requests',
        'tkinter'  # 通常內建，但確保可用
    ]
    
    print("📦 安裝必要套件...")
    for package in requirements:
        try:
            if package == 'tkinter':
                import tkinter
                print(f"✅ {package} 已可用")
            else:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} 安裝完成")
        except Exception as e:
            print(f"⚠️ {package} 安裝失敗: {e}")

def download_file(url, filename):
    """下載檔案"""
    print(f"📥 下載 {filename}...")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ {filename} 下載完成")
        return True
    except Exception as e:
        print(f"❌ 下載 {filename} 失敗: {e}")
        return False

def get_github_release_assets(repo, tag):
    """獲取 GitHub release 的 assets 資訊"""
    try:
        api_url = f"https://api.github.com/repos/{repo}/releases/tags/{tag}"
        response = requests.get(api_url)
        response.raise_for_status()
        release_data = response.json()
        return release_data.get('assets', [])
    except Exception as e:
        print(f"❌ 無法獲取 release 資訊: {e}")
        return []

def download_mp4_merge():
    """下載 mp4-merge 工具"""
    print("\n🔧 下載 mp4-merge 工具...")

    # 嘗試從 GitHub API 獲取正確的下載連結
    assets = get_github_release_assets("gyroflow/mp4-merge", "v0.1.11")

    # 尋找 Windows 版本
    windows_asset = None
    for asset in assets:
        if 'windows' in asset['name'].lower() or 'win' in asset['name'].lower():
            windows_asset = asset
            break

    if windows_asset:
        url = windows_asset['browser_download_url']
        filename = windows_asset['name']
        print(f"📥 找到檔案: {filename}")

        # 檢查是否為 ARM64 版本，如果是則尋找 x64 版本
        if 'arm64' in filename.lower():
            print("⚠️ 找到的是 ARM64 版本，尋找 x64 版本...")
            for asset in assets:
                name_lower = asset['name'].lower()
                if ('windows' in name_lower or 'win' in name_lower) and 'arm64' not in name_lower:
                    windows_asset = asset
                    url = windows_asset['browser_download_url']
                    filename = windows_asset['name']
                    print(f"📥 找到 x64 版本: {filename}")
                    break
    else:
        # 備用下載連結 - 嘗試多個可能的檔名
        print("⚠️ 無法自動找到 Windows 版本，嘗試備用連結...")
        backup_urls = [
            "https://github.com/gyroflow/mp4-merge/releases/download/v0.1.11/mp4_merge-windows64.exe",
            "https://github.com/gyroflow/mp4-merge/releases/download/v0.1.11/mp4_merge-windows.exe",
            "https://github.com/gyroflow/mp4-merge/releases/download/v0.1.11/mp4_merge.exe"
        ]

        for backup_url in backup_urls:
            filename = backup_url.split('/')[-1]
            print(f"🔄 嘗試: {filename}")
            if download_file(backup_url, filename):
                url = backup_url
                break
        else:
            url = backup_urls[0]
            filename = backup_urls[0].split('/')[-1]

    if download_file(url, filename):
        # 如果是 exe 檔案，直接移動到工具目錄
        if filename.endswith('.exe'):
            os.makedirs("tools/mp4-merge", exist_ok=True)
            import shutil
            shutil.move(filename, f"tools/mp4-merge/{filename}")
            print("✅ mp4-merge 安裝完成")
            return True
        # 如果是壓縮檔，解壓縮
        elif filename.endswith('.zip'):
            try:
                with zipfile.ZipFile(filename, 'r') as zip_ref:
                    zip_ref.extractall("tools/mp4-merge")
                print("✅ mp4-merge 解壓縮完成")
                os.remove(filename)  # 刪除壓縮檔
                return True
            except Exception as e:
                print(f"❌ mp4-merge 解壓縮失敗: {e}")
                return False

    # 如果自動下載失敗，提供手動下載指引
    print("❌ 自動下載失敗")
    print("💡 請手動下載 mp4-merge:")
    print("   1. 前往: https://github.com/gyroflow/mp4-merge/releases/latest")
    print("   2. 下載 Windows 版本")
    print("   3. 解壓縮到 tools/mp4-merge/ 目錄")
    return False

def download_vidmerger():
    """下載 vidmerger 工具"""
    print("\n🔧 下載 vidmerger 工具...")

    # 嘗試從 GitHub API 獲取正確的下載連結
    assets = get_github_release_assets("tgotwig/vidmerger", "0.4.0")

    # 尋找 Windows 版本
    windows_asset = None
    for asset in assets:
        if 'windows' in asset['name'].lower() or 'win' in asset['name'].lower():
            windows_asset = asset
            break

    if windows_asset:
        url = windows_asset['browser_download_url']
        filename = windows_asset['name']
        print(f"📥 找到檔案: {filename}")
    else:
        # 備用下載連結
        print("⚠️ 無法自動找到 Windows 版本，使用備用連結...")
        url = "https://github.com/tgotwig/vidmerger/releases/download/0.4.0/vidmerger.exe"
        filename = "vidmerger.exe"

    if download_file(url, filename):
        # 如果是 exe 檔案，直接移動到工具目錄
        if filename.endswith('.exe'):
            os.makedirs("tools/vidmerger", exist_ok=True)
            import shutil
            shutil.move(filename, f"tools/vidmerger/{filename}")
            print("✅ vidmerger 安裝完成")
            return True
        # 如果是壓縮檔，解壓縮
        elif filename.endswith('.zip') or filename.endswith('.tar.gz'):
            try:
                if filename.endswith('.zip'):
                    with zipfile.ZipFile(filename, 'r') as zip_ref:
                        zip_ref.extractall("tools/vidmerger")
                else:  # tar.gz
                    import tarfile
                    with tarfile.open(filename, 'r:gz') as tar_ref:
                        tar_ref.extractall("tools/vidmerger")
                print("✅ vidmerger 解壓縮完成")
                os.remove(filename)  # 刪除壓縮檔
                return True
            except Exception as e:
                print(f"❌ vidmerger 解壓縮失敗: {e}")
                return False

    # 如果自動下載失敗，提供手動下載指引
    print("❌ 自動下載失敗")
    print("💡 請手動下載 vidmerger:")
    print("   1. 前往: https://github.com/tgotwig/vidmerger/releases/latest")
    print("   2. 下載 Windows 版本")
    print("   3. 解壓縮到 tools/vidmerger/ 目錄")
    return False

def check_ffmpeg():
    """檢查 FFmpeg 是否已安裝"""
    print("\n🎬 檢查 FFmpeg...")
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg 已安裝")
            return True
    except FileNotFoundError:
        pass
    
    print("⚠️ FFmpeg 未安裝")
    print("💡 vidmerger 需要 FFmpeg 才能運作")
    print("📖 請從 https://ffmpeg.org/download.html 下載並安裝 FFmpeg")
    return False

def create_directories():
    """創建必要的目錄"""
    directories = ["tools", "tools/mp4-merge", "tools/vidmerger"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    print("✅ 目錄結構創建完成")

def main():
    """主函數"""
    print("🎬 影片合併工具安裝程式")
    print("=" * 50)
    
    # 檢查系統
    check_python_version()
    
    # 檢查作業系統
    if platform.system() != "Windows":
        print("⚠️ 此腳本主要為 Windows 設計")
        print("💡 Linux/Mac 用戶請參考各工具的官方安裝說明")
    
    # 創建目錄
    create_directories()
    
    # 安裝 Python 套件
    install_requirements()
    
    # 下載工具
    mp4_success = download_mp4_merge()
    vid_success = download_vidmerger()
    
    # 檢查 FFmpeg
    ffmpeg_ok = check_ffmpeg()
    
    # 總結
    print("\n" + "=" * 50)
    print("📋 安裝總結:")
    print(f"   mp4-merge: {'✅' if mp4_success else '❌'}")
    print(f"   vidmerger: {'✅' if vid_success else '❌'}")
    print(f"   FFmpeg: {'✅' if ffmpeg_ok else '⚠️'}")
    
    if mp4_success and vid_success:
        print("\n🎉 工具下載完成！")
        print("💡 現在可以執行 'python video_merger_gui.py' 來啟動程式")
    else:
        print("\n❌ 部分工具下載失敗，請檢查網路連線並重試")
    
    if not ffmpeg_ok:
        print("\n⚠️ 請記得安裝 FFmpeg 以使用 vidmerger 功能")

if __name__ == "__main__":
    main()
